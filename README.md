# Proyecto Vite + TypeScript + Pug + Tailwind CSS

Este proyecto combina Vite, TypeScript, Pug templates y Tailwind CSS para crear una aplicación web moderna con templates dinámicos.

## 🚀 Características

- **Vite**: Build tool rápido con hot reload
- **TypeScript**: Tipado estático para mejor desarrollo
- **Pug**: Motor de templates limpio y expresivo
- **Tailwind CSS**: Framework de CSS utility-first

## 📁 Estructura del proyecto

```
src/
├── templates/          # Archivos Pug
│   ├── 01-Intro.pug   # Template de introducción
│   └── 02-About.pug   # Template de información
├── main.ts            # Archivo principal
├── counter.ts         # Lógica del contador
└── style.css          # Estilos de Tailwind
```

## 🎯 Cómo usar

### Cambiar templates dinámicamente

Puedes cambiar entre templates usando la función global:

```javascript
window.changeTemplate('01-Intro')  // Cargar template de intro
window.changeTemplate('02-About')  // Cargar template de about
```

### Agregar nuevos templates

1. Crea un nuevo archivo `.pug` en `src/templates/`
2. Importa el template en `src/main.ts`
3. Agrégalo al objeto `templates`

Ejemplo:
```typescript
import newTemplate from './templates/03-New.pug'

const templates: Record<string, string> = {
  '01-Intro': introTemplate,
  '02-About': aboutTemplate,
  '03-New': newTemplate  // Nuevo template
}
```

## 🛠️ Comandos disponibles

```bash
npm run dev      # Iniciar servidor de desarrollo
npm run build    # Construir para producción
npm run preview  # Vista previa de la build
```

## 🎨 Estilos con Tailwind

Los templates usan clases de Tailwind CSS para el styling. Puedes personalizar los estilos editando `tailwind.config.js`.

## 📝 Notas

- Los templates se cargan dinámicamente sin recargar la página
- El contador se reconfigura automáticamente al cambiar templates
- Los archivos Pug se compilan automáticamente por Vite
