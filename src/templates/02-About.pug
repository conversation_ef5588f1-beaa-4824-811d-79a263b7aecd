div.min-h-screen.bg-gradient-to-br.from-purple-50.to-pink-100.flex.items-center.justify-center.p-4
  div.max-w-4xl.mx-auto.text-center
    div.mb-8
      h1.text-6xl.font-bold.text-gray-800.mb-4.bg-gradient-to-r.from-purple-600.to-pink-600.bg-clip-text.text-transparent
        | Acerca de este proyecto
    
    div.bg-white.rounded-xl.shadow-lg.p-8.mb-8.border.border-gray-200.max-w-2xl.mx-auto
      div.space-y-6.text-left
        div
          h3.text-xl.font-semibold.text-gray-800.mb-2.flex.items-center
            span.mr-2 🚀
            | Tecnologías utilizadas
          ul.list-disc.list-inside.text-gray-600.space-y-1
            li Vite - Build tool rápido
            li TypeScript - Tipado estático
            li Pug - Motor de templates
            li Tailwind CSS - Framework de CSS
        
        div
          h3.text-xl.font-semibold.text-gray-800.mb-2.flex.items-center
            span.mr-2 ✨
            | Características
          ul.list-disc.list-inside.text-gray-600.space-y-1
            li Templates Pug dinámicos
            li Estilos con Tailwind CSS
            li Hot reload en desarrollo
            li TypeScript para mejor DX
    
    div.flex.justify-center.space-x-4.mb-8
      button.bg-gradient-to-r.from-purple-500.to-pink-600.text-white.px-6.py-3.rounded-lg.font-semibold.hover:from-purple-600.hover:to-pink-700.transition-all.duration-200.transform.hover:scale-105.shadow-md.hover:shadow-lg(onclick="window.changeTemplate('01-Intro')")
        | ← Volver al Inicio
      
      button#counter.bg-gradient-to-r.from-green-500.to-blue-600.text-white.px-6.py-3.rounded-lg.font-semibold.hover:from-green-600.hover:to-blue-700.transition-all.duration-200.transform.hover:scale-105.shadow-md.hover:shadow-lg
        | Contador
    
    p.text-gray-600.text-lg.max-w-2xl.mx-auto.leading-relaxed
      | Este es un ejemplo de cómo puedes cambiar dinámicamente entre diferentes templates Pug. 
      | Cada template puede tener su propio diseño y funcionalidad.
    
    div.mt-8.flex.justify-center.space-x-4
      span.inline-flex.items-center.px-3.py-1.rounded-full.text-sm.font-medium.bg-purple-100.text-purple-800
        | 📄 Template 2
      span.inline-flex.items-center.px-3.py-1.rounded-full.text-sm.font-medium.bg-pink-100.text-pink-800
        | 🎨 Diseño único
