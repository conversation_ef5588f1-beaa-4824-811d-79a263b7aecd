div.min-h-screen.bg-gradient-to-br.from-blue-50.to-indigo-100.flex.items-center.justify-center.p-4
  div.max-w-4xl.mx-auto.text-center
    div.mb-8
      a(href="https://vite.dev" target="_blank").inline-block.mr-4
        img(src="/vite.svg" alt="Vite logo").h-24.w-24.hover:drop-shadow-lg.transition-all.duration-300.hover:scale-110
      a(href="https://www.typescriptlang.org/" target="_blank").inline-block
        img(src="/src/typescript.svg" alt="TypeScript logo").h-24.w-24.hover:drop-shadow-lg.transition-all.duration-300.hover:scale-110
    
    h1.text-6xl.font-bold.text-gray-800.mb-8.bg-gradient-to-r.from-blue-600.to-purple-600.bg-clip-text.text-transparent
      | Vite + TypeScript + Pug
    
    div.bg-white.rounded-xl.shadow-lg.p-8.mb-8.border.border-gray-200
      div.flex.justify-center.space-x-4
        button#counter.bg-gradient-to-r.from-blue-500.to-purple-600.text-white.px-6.py-3.rounded-lg.font-semibold.hover:from-blue-600.hover:to-purple-700.transition-all.duration-200.transform.hover:scale-105.shadow-md.hover:shadow-lg
          | Contador
        button.bg-gradient-to-r.from-green-500.to-teal-600.text-white.px-6.py-3.rounded-lg.font-semibold.hover:from-green-600.hover:to-teal-700.transition-all.duration-200.transform.hover:scale-105.shadow-md.hover:shadow-lg(onclick="window.changeTemplate('02-About')")
          | Acerca de →
    
    p.text-gray-600.text-lg.max-w-2xl.mx-auto.leading-relaxed
      | ¡Bienvenido a tu nueva configuración con 
      span.font-semibold.text-blue-600 Pug templates
      |  y 
      span.font-semibold.text-purple-600 Tailwind CSS
      | ! Haz clic en los logos de Vite y TypeScript para aprender más.
    
    div.mt-8.flex.justify-center.space-x-4
      span.inline-flex.items-center.px-3.py-1.rounded-full.text-sm.font-medium.bg-blue-100.text-blue-800
        | 🚀 Vite
      span.inline-flex.items-center.px-3.py-1.rounded-full.text-sm.font-medium.bg-purple-100.text-purple-800
        | 📝 Pug
      span.inline-flex.items-center.px-3.py-1.rounded-full.text-sm.font-medium.bg-green-100.text-green-800
        | 🎨 Tailwind
