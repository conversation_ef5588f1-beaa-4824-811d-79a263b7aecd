import './style.css'
import { setupCounter } from './counter.ts'
import pug from 'pug'

// Función para cargar y renderizar templates Pug
async function loadPugTemplate(templateName: string): Promise<string> {
  try {
    // Importar el archivo Pug como texto
    const response = await fetch(`/src/templates/${templateName}.pug`)
    const pugTemplate = await response.text()

    // Compilar y renderizar el template
    const compiledFunction = pug.compile(pugTemplate)
    return compiledFunction()
  } catch (error) {
    console.error(`Error loading template ${templateName}:`, error)
    return '<div class="text-red-500 p-4">Error loading template</div>'
  }
}

// Función para cambiar el template actual
async function changeTemplate(templateName: string) {
  const appElement = document.querySelector<HTMLDivElement>('#app')!
  const html = await loadPugTemplate(templateName)
  appElement.innerHTML = html

  // Reconfigurar el contador si existe
  const counterButton = document.querySelector<HTMLButtonElement>('#counter')
  if (counterButton) {
    setupCounter(counterButton)
  }
}

// Cargar el template inicial
changeTemplate('01-Intro')

// Exportar la función para uso externo
declare global {
  interface Window {
    changeTemplate: (templateName: string) => Promise<void>
  }
}

window.changeTemplate = changeTemplate
